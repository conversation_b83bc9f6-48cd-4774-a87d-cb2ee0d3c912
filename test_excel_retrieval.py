#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据检索效果测试
"""
import sys
import pandas as pd
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

def analyze_excel_data():
    """分析Excel数据的内容和结构"""
    print("📊 Excel数据分析")
    print("=" * 60)
    
    data_dir = Path("./data")
    excel_files = list(data_dir.glob("*.xlsx"))
    
    print(f"发现 {len(excel_files)} 个Excel文件:")
    
    total_rows = 0
    file_analysis = {}
    
    for excel_file in excel_files:
        print(f"\n📄 分析文件: {excel_file.name}")
        try:
            # 读取所有工作表
            sheets = pd.read_excel(excel_file, sheet_name=None, engine='openpyxl')
            
            file_rows = 0
            sheet_info = {}
            
            for sheet_name, df in sheets.items():
                if not df.empty:
                    rows = len(df)
                    cols = len(df.columns)
                    file_rows += rows
                    
                    # 获取列名
                    columns = df.columns.tolist()
                    
                    # 获取数据样本
                    sample_data = []
                    for i in range(min(3, len(df))):
                        row_data = {}
                        for col in columns[:5]:  # 只显示前5列
                            row_data[col] = str(df.iloc[i][col])[:50]  # 限制长度
                        sample_data.append(row_data)
                    
                    sheet_info[sheet_name] = {
                        'rows': rows,
                        'cols': cols,
                        'columns': columns,
                        'sample': sample_data
                    }
                    
                    print(f"  工作表 '{sheet_name}': {rows} 行 × {cols} 列")
                    print(f"    列名: {', '.join(columns[:5])}{'...' if len(columns) > 5 else ''}")
                    
                    # 显示数据样本
                    if sample_data:
                        print(f"    数据样本:")
                        for j, sample in enumerate(sample_data[:2]):
                            print(f"      行{j+1}: {sample}")
            
            file_analysis[excel_file.name] = {
                'total_rows': file_rows,
                'sheets': sheet_info
            }
            
            total_rows += file_rows
            print(f"  文件总行数: {file_rows}")
            
        except Exception as e:
            print(f"  ❌ 读取失败: {str(e)}")
    
    print(f"\n📈 总计: {total_rows} 行Excel数据")
    return file_analysis

def test_excel_processing():
    """测试Excel数据处理"""
    print("\n🔧 Excel数据处理测试")
    print("=" * 60)
    
    try:
        from app import load_excels, create_nodes_from_excel, init_node_parser
        
        # 1. 加载Excel数据
        print("1. 加载Excel数据...")
        excel_data = load_excels("./data")
        print(f"   加载了 {len(excel_data)} 条Excel记录")
        
        if excel_data:
            # 显示数据样本
            print("\n   数据样本:")
            for i, item in enumerate(excel_data[:3]):
                print(f"   样本{i+1}:")
                print(f"     内容: {item['content'][:100]}...")
                print(f"     元数据: {item['metadata']}")
        
        # 2. 创建文本节点
        print("\n2. 创建文本节点...")
        node_parser = init_node_parser()
        excel_nodes = create_nodes_from_excel(excel_data, node_parser)
        print(f"   创建了 {len(excel_nodes)} 个文本节点")
        
        if excel_nodes:
            print("\n   节点样本:")
            for i, node in enumerate(excel_nodes[:3]):
                print(f"   节点{i+1}:")
                print(f"     ID: {node.id_}")
                print(f"     文本: {node.text[:100]}...")
                print(f"     元数据: {node.metadata}")
        
        return excel_data, excel_nodes
        
    except Exception as e:
        print(f"❌ Excel处理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None

def test_excel_retrieval():
    """测试Excel数据检索效果"""
    print("\n🔍 Excel检索效果测试")
    print("=" * 60)
    
    try:
        from app import load_index, OptimizedModelManager
        
        # 加载知识库
        print("1. 加载知识库...")
        index = load_index()
        if index is None:
            print("❌ 知识库加载失败")
            return
        
        # 统计Excel节点
        all_nodes = list(index.docstore.docs.values())
        excel_nodes = [node for node in all_nodes if 
                      node.metadata.get('content_type') == 'excel_row']
        
        print(f"   知识库总节点数: {len(all_nodes)}")
        print(f"   Excel节点数: {len(excel_nodes)}")
        
        if not excel_nodes:
            print("⚠️ 知识库中没有Excel数据")
            return
        
        # 初始化检索器
        print("\n2. 初始化检索器...")
        model_manager = OptimizedModelManager()
        embed_model = model_manager.get_embed_model()
        
        retriever = index.as_retriever(similarity_top_k=10)
        
        # 测试查询
        test_queries = [
            "陶岔渠首引水闸的流量数据",
            "严陵河渡槽的时序数据",
            "渠首段的地理信息",
            "各个渡槽的进口节制闸数据",
            "湍河渡槽的运行参数"
        ]
        
        print("\n3. 执行检索测试...")
        for i, query in enumerate(test_queries, 1):
            print(f"\n   测试查询 {i}: {query}")
            
            try:
                # 执行检索
                retrieved_nodes = retriever.retrieve(query)
                
                # 统计Excel相关结果
                excel_results = [node for node in retrieved_nodes if 
                               node.metadata.get('content_type') == 'excel_row']
                
                print(f"     总检索结果: {len(retrieved_nodes)}")
                print(f"     Excel相关结果: {len(excel_results)}")
                
                # 显示Excel结果详情
                if excel_results:
                    print(f"     Excel结果详情:")
                    for j, node in enumerate(excel_results[:3]):
                        score = getattr(node, 'score', 'N/A')
                        source_file = node.metadata.get('source_file', 'unknown')
                        sheet_name = node.metadata.get('sheet_name', 'unknown')
                        print(f"       {j+1}. 相关度: {score}")
                        print(f"          文件: {source_file}")
                        print(f"          工作表: {sheet_name}")
                        print(f"          内容: {node.text[:80]}...")
                else:
                    print(f"     ⚠️ 没有检索到Excel相关结果")
                    
            except Exception as e:
                print(f"     ❌ 检索失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel检索测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_excel_qa():
    """测试Excel数据问答效果"""
    print("\n💬 Excel问答效果测试")
    print("=" * 60)
    
    try:
        from app import OptimizedModelManager, load_index
        from llama_index.core.chat_engine import ContextChatEngine
        from llama_index.core.memory import ChatMemoryBuffer
        
        # 加载组件
        print("1. 初始化问答系统...")
        index = load_index()
        if index is None:
            print("❌ 知识库加载失败")
            return
        
        model_manager = OptimizedModelManager()
        llm = model_manager.get_llm()
        retriever = index.as_retriever(similarity_top_k=5)
        
        # 创建简单的问答引擎
        chat_engine = ContextChatEngine.from_defaults(
            retriever=retriever,
            llm=llm,
            memory=ChatMemoryBuffer.from_defaults(token_limit=2048),
            verbose=False
        )
        
        # 测试Excel相关问题
        excel_questions = [
            "陶岔渠首引水闸有什么数据？",
            "严陵河渡槽的时序数据包含哪些信息？",
            "各个渡槽的流量情况如何？"
        ]
        
        print("\n2. 执行问答测试...")
        for i, question in enumerate(excel_questions, 1):
            print(f"\n   问题 {i}: {question}")
            
            try:
                # 注意：这里不实际调用LLM，只测试检索部分
                retrieved_nodes = retriever.retrieve(question)
                excel_nodes = [node for node in retrieved_nodes if 
                             node.metadata.get('content_type') == 'excel_row']
                
                print(f"     检索到 {len(excel_nodes)} 个Excel相关节点")
                
                if excel_nodes:
                    print(f"     相关Excel数据:")
                    for j, node in enumerate(excel_nodes[:2]):
                        source = node.metadata.get('source_file', 'unknown')
                        print(f"       - {source}: {node.text[:60]}...")
                else:
                    print(f"     ⚠️ 未找到相关Excel数据")
                    
            except Exception as e:
                print(f"     ❌ 问答测试失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel问答测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🎯 Excel数据检索效果全面测试")
    print("=" * 80)
    
    # 1. 分析Excel数据
    file_analysis = analyze_excel_data()
    
    # 2. 测试数据处理
    excel_data, excel_nodes = test_excel_processing()
    
    # 3. 测试检索效果
    retrieval_success = test_excel_retrieval()
    
    # 4. 测试问答效果
    qa_success = test_excel_qa()
    
    # 总结报告
    print("\n" + "=" * 80)
    print("📊 Excel检索效果测试报告")
    print("=" * 80)
    
    if file_analysis:
        total_files = len(file_analysis)
        total_rows = sum(info['total_rows'] for info in file_analysis.values())
        print(f"✅ 数据分析: {total_files} 个Excel文件, {total_rows} 行数据")
    
    if excel_data and excel_nodes:
        print(f"✅ 数据处理: {len(excel_data)} 条记录 → {len(excel_nodes)} 个节点")
    else:
        print("❌ 数据处理: 失败")
    
    if retrieval_success:
        print("✅ 检索测试: 通过")
    else:
        print("❌ 检索测试: 失败")
    
    if qa_success:
        print("✅ 问答测试: 通过")
    else:
        print("❌ 问答测试: 失败")
    
    print("\n💡 Excel检索效果评估:")
    if retrieval_success and qa_success:
        print("🎉 Excel数据检索效果良好，可以有效支持相关查询")
    elif retrieval_success:
        print("⚠️ Excel数据检索基本正常，但问答效果需要优化")
    else:
        print("❌ Excel数据检索存在问题，需要进一步调试")
    
    print("\n🔧 优化建议:")
    print("1. 确保Excel数据格式规范，列名清晰")
    print("2. 考虑为Excel数据添加更多上下文信息")
    print("3. 优化Excel数据的文本表示格式")
    print("4. 调整检索参数以提高Excel数据的召回率")

if __name__ == "__main__":
    main()
