#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证1月1日12:00数据是否存在
"""
import pandas as pd
from pathlib import Path

def check_jan1_12pm_data():
    """检查1月1日12:00数据是否真的存在"""
    print("🔍 验证2020-01-01 12:00数据存在性")
    print("=" * 50)
    
    data_dir = Path("data")
    if not data_dir.exists():
        print("❌ data目录不存在")
        return
    
    excel_files = list(data_dir.glob("*.xlsx"))
    found_target_data = False
    
    for excel_file in excel_files:
        if "十二里河" in excel_file.name:
            print(f"📊 检查文件: {excel_file.name}")
            
            try:
                df = pd.read_excel(excel_file, dtype=str)
                
                # 查找时间列
                time_cols = [col for col in df.columns if '时间' in str(col)]
                if not time_cols:
                    print("   ⚠️ 未找到时间列")
                    continue
                
                time_col = time_cols[0]
                print(f"   时间列: {time_col}")
                
                # 精确查找目标时间点
                target_patterns = [
                    "2020-01-01 12:00:00",
                    "2020-01-01 12:00",
                    "2020/01/01 12:00:00",
                    "2020/01/01 12:00"
                ]
                
                print(f"\n   🎯 查找目标时间点:")
                for pattern in target_patterns:
                    matches = df[df[time_col].str.contains(pattern, na=False)]
                    print(f"     '{pattern}': {len(matches)} 条")
                    
                    if len(matches) > 0:
                        found_target_data = True
                        print(f"     ✅ 找到数据!")
                        sample = matches.iloc[0]
                        print(f"     示例: {dict(sample)}")
                        break
                
                if not found_target_data:
                    # 查看1月1日的所有数据
                    jan1_data = df[df[time_col].str.contains("2020-01-01", na=False)]
                    print(f"\n   📅 2020-01-01的所有数据: {len(jan1_data)} 条")
                    
                    if len(jan1_data) > 0:
                        print(f"   实际时间点:")
                        unique_times = jan1_data[time_col].unique()
                        for time_val in sorted(unique_times):
                            print(f"     {time_val}")
                    else:
                        print(f"   ❌ 2020-01-01 整天都没有数据!")
                
                # 查看1月2日12:00的数据作为对比
                jan2_12pm = df[df[time_col].str.contains("2020-01-02 12:00", na=False)]
                print(f"\n   📅 2020-01-02 12:00数据: {len(jan2_12pm)} 条")
                if len(jan2_12pm) > 0:
                    print(f"   ✅ 确认存在1月2日12:00数据")
                
            except Exception as e:
                print(f"   ❌ 文件读取失败: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"📋 检查结果:")
    
    if found_target_data:
        print("✅ 2020-01-01 12:00:00 数据存在于Excel中")
        print("🔧 问题可能是:")
        print("   1. 知识库构建时数据丢失")
        print("   2. 检索算法匹配问题")
        print("   3. 时间格式转换问题")
        print("\n💡 建议: 重新构建知识库")
    else:
        print("❌ 2020-01-01 12:00:00 数据确实不存在于Excel中")
        print("🎯 这就是检索不到的根本原因!")
        print("\n💡 解释:")
        print("   - 水利设施数据采集可能不是每小时整点")
        print("   - 可能是每2小时、4小时或其他间隔")
        print("   - 1月1日12:00这个时间点可能确实没有采集数据")
        print("   - 但1月2日12:00有数据，所以能检索到")
        
        print("\n🛠️ 解决方案:")
        print("   1. 修改查询策略 - 使用时间范围而不是精确时间点")
        print("   2. 提供最接近的时间点数据")
        print("   3. 在回答中说明数据采集间隔情况")

if __name__ == "__main__":
    check_jan1_12pm_data()
