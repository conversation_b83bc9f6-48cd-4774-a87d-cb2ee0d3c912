#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彻底修复1月1日数据问题
"""
import pandas as pd
from pathlib import Path
import sys
sys.path.append('.')

def check_and_fix_excel_data():
    """检查并修复Excel数据"""
    print("🔍 检查Excel数据中的1月1日问题")
    print("=" * 50)
    
    data_dir = Path("data")
    excel_files = list(data_dir.glob("*.xlsx"))
    
    found_jan1_data = False
    
    for excel_file in excel_files:
        if "十二里河" in excel_file.name:
            print(f"📁 检查文件: {excel_file.name}")
            
            try:
                # 读取原始数据
                df = pd.read_excel(excel_file, dtype=str)
                print(f"   总行数: {len(df)}")
                
                # 查找时间列
                time_cols = [col for col in df.columns if '时间' in str(col)]
                if not time_cols:
                    print("   ❌ 没有找到时间列")
                    continue
                
                time_col = time_cols[0]
                print(f"   时间列: {time_col}")
                
                # 显示所有唯一的时间值
                unique_times = df[time_col].dropna().unique()
                print(f"   唯一时间值数量: {len(unique_times)}")
                
                # 查找所有包含2020-01-01的数据
                jan1_mask = df[time_col].astype(str).str.contains('2020-01-01', na=False)
                jan1_data = df[jan1_mask]
                
                print(f"   🎯 2020-01-01相关数据: {len(jan1_data)} 行")
                
                if len(jan1_data) > 0:
                    found_jan1_data = True
                    print("   详细的1月1日数据:")
                    for i, (idx, row) in enumerate(jan1_data.iterrows()):
                        time_val = str(row[time_col]).strip()
                        print(f"     第{i+1}行: '{time_val}'")
                        
                        # 检查是否有12点的数据
                        if '12' in time_val:
                            print(f"       ✅ 找到12点数据: '{time_val}'")
                            
                            # 显示这行的所有数据
                            print("       完整数据:")
                            for col in df.columns:
                                val = row[col]
                                if pd.notna(val) and str(val).strip():
                                    print(f"         {col}: {val}")
                
                # 查找所有包含2020-01-02的数据
                jan2_mask = df[time_col].astype(str).str.contains('2020-01-02', na=False)
                jan2_data = df[jan2_mask]
                print(f"   🎯 2020-01-02相关数据: {len(jan2_data)} 行")
                
                if len(jan2_data) > 0:
                    print("   1月2日数据样本:")
                    for i, (idx, row) in enumerate(jan2_data.head(3).iterrows()):
                        time_val = str(row[time_col]).strip()
                        print(f"     第{i+1}行: '{time_val}'")
                
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
    
    return found_jan1_data

def check_knowledge_base():
    """检查知识库中的数据"""
    print("\n🔍 检查知识库中的1月1日数据")
    print("=" * 50)
    
    try:
        from app import load_index
        
        # 加载知识库
        index = load_index()
        if index is None:
            print("❌ 知识库加载失败")
            return False
        
        # 获取所有节点
        all_nodes = list(index.docstore.docs.values())
        print(f"📊 总节点数: {len(all_nodes)}")
        
        # 查找1月1日相关的节点
        jan1_nodes = []
        jan1_12_nodes = []
        
        for node in all_nodes:
            if hasattr(node, 'text') and node.text:
                text = node.text
                if "2020-01-01" in text:
                    jan1_nodes.append(node)
                    if "12:00:00" in text or " 12 " in text:
                        jan1_12_nodes.append(node)
        
        print(f"🎯 包含'2020-01-01'的节点: {len(jan1_nodes)} 个")
        print(f"🎯 包含'2020-01-01'和'12'的节点: {len(jan1_12_nodes)} 个")
        
        if jan1_12_nodes:
            print("\n✅ 找到1月1日12点的节点:")
            for i, node in enumerate(jan1_12_nodes[:3]):
                print(f"   节点{i+1}:")
                print(f"     文本: {node.text[:200]}...")
                print(f"     类型: {node.metadata.get('content_type', 'unknown')}")
                print(f"     来源: {node.metadata.get('file_name', 'unknown')}")
        
        elif jan1_nodes:
            print("\n⚠️ 找到1月1日的节点，但没有12点数据:")
            for i, node in enumerate(jan1_nodes[:3]):
                print(f"   节点{i+1}:")
                print(f"     文本: {node.text[:200]}...")
                print(f"     类型: {node.metadata.get('content_type', 'unknown')}")
        
        else:
            print("\n❌ 知识库中没有找到任何1月1日的数据")
        
        return len(jan1_12_nodes) > 0
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_retrieval():
    """测试检索功能"""
    print("\n🔍 测试检索功能")
    print("=" * 50)
    
    try:
        from app import load_index, TimeRangeRetriever
        from llama_index.core.schema import QueryBundle
        
        # 加载知识库
        index = load_index()
        if index is None:
            print("❌ 知识库加载失败")
            return
        
        # 创建检索器
        base_retriever = index.as_retriever(similarity_top_k=20)
        time_retriever = TimeRangeRetriever(
            base_retriever=base_retriever,
            index=index,
            similarity_top_k=20
        )
        
        # 测试查询
        test_query = "十二里河渡槽进口节制闸在2020-01-01 12:00:00的数据"
        print(f"🎯 测试查询: {test_query}")
        
        query_bundle = QueryBundle(query_str=test_query)
        results = time_retriever._retrieve(query_bundle)
        
        print(f"   检索结果: {len(results)} 个")
        
        jan1_results = []
        for result in results:
            if "2020-01-01" in result.node.text and "12" in result.node.text:
                jan1_results.append(result)
        
        print(f"   包含1月1日12点的结果: {len(jan1_results)} 个")
        
        if jan1_results:
            print("\n✅ 找到1月1日12点数据:")
            for i, result in enumerate(jan1_results[:2]):
                score = getattr(result, 'score', 0)
                print(f"   结果{i+1} (分数: {score:.3f}):")
                print(f"     {result.node.text[:150]}...")
        else:
            print("\n❌ 没有找到1月1日12点的数据")
            
            # 显示一些相关结果
            print("\n相关结果:")
            for i, result in enumerate(results[:3]):
                score = getattr(result, 'score', 0)
                print(f"   结果{i+1} (分数: {score:.3f}):")
                print(f"     {result.node.text[:100]}...")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🎯 彻底修复1月1日数据问题")
    print("=" * 60)
    
    # 1. 检查Excel原始数据
    excel_has_jan1 = check_and_fix_excel_data()
    
    # 2. 检查知识库
    kb_has_jan1 = check_knowledge_base()
    
    # 3. 测试检索
    test_retrieval()
    
    print(f"\n" + "=" * 60)
    print("📋 问题诊断结果")
    print("=" * 60)
    
    if excel_has_jan1:
        print("✅ Excel文件中存在1月1日数据")
    else:
        print("❌ Excel文件中没有1月1日数据")
    
    if kb_has_jan1:
        print("✅ 知识库中存在1月1日12点数据")
    else:
        print("❌ 知识库中没有1月1日12点数据")
    
    if excel_has_jan1 and not kb_has_jan1:
        print("\n🔧 建议解决方案:")
        print("1. Excel中有数据但知识库中没有，需要重新构建知识库")
        print("2. 运行: python rebuild_kb.py")
        print("3. 然后重新启动应用")
    elif not excel_has_jan1:
        print("\n🔧 建议解决方案:")
        print("1. Excel文件本身就没有1月1日12:00的数据")
        print("2. 需要检查数据源或使用其他时间点的数据")
    elif kb_has_jan1:
        print("\n🔧 建议解决方案:")
        print("1. 数据存在，可能是检索或提示词问题")
        print("2. 检查TimeRangeRetriever是否正确工作")

if __name__ == "__main__":
    main()
