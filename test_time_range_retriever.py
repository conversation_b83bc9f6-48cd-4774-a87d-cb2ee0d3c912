#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能时间范围检索器
"""
import sys
sys.path.append('.')

def test_time_range_extraction():
    """测试时间范围提取功能"""
    print("🧪 测试时间范围提取")
    print("=" * 40)
    
    # 导入TimeRangeRetriever类
    from app import TimeRangeRetriever
    
    # 创建一个临时实例用于测试
    class MockRetriever:
        def retrieve(self, query_bundle):
            return []
    
    class MockIndex:
        pass
    
    retriever = TimeRangeRetriever(MockRetriever(), MockIndex())
    
    # 测试用例
    test_queries = [
        "十二里河渡槽进口节制闸在2020-01-01至2020-01-02的调度运行情况",
        "十二里河渡槽2020年1月1日至2日期间的数据",
        "2020-01-01 12:00:00至2020-01-02 12:00:00的运行数据",
        "1月1日12:00至1月2日12:00期间的调度情况",
        "十二里河渡槽在2020-01-01 12:00:00的运行数据",  # 单时间点
        "十二里河渡槽的基本信息",  # 非时间查询
    ]
    
    for query in test_queries:
        print(f"\n🔍 查询: {query}")
        time_info = retriever._extract_time_range(query)
        
        if time_info:
            if time_info.get('has_time_range'):
                print(f"   ✅ 时间范围查询")
                print(f"   起始时间: {time_info.get('start_time')}")
                print(f"   结束时间: {time_info.get('end_time')}")
                print(f"   模式类型: {time_info.get('pattern_type')}")
            elif time_info.get('single_time'):
                print(f"   ✅ 单时间点查询")
            else:
                print(f"   ⚪ 其他时间查询")
        else:
            print(f"   ⚪ 非时间查询")

def test_full_retrieval():
    """测试完整的检索功能"""
    print("\n🔍 测试完整检索功能")
    print("=" * 40)
    
    try:
        from app import load_index, TimeRangeRetriever
        from llama_index.core.schema import QueryBundle
        
        # 加载知识库
        print("📚 加载知识库...")
        index = load_index()
        
        if index is None:
            print("❌ 知识库加载失败")
            return False
        
        # 创建基础检索器
        base_retriever = index.as_retriever(similarity_top_k=20)
        
        # 创建智能时间范围检索器
        time_retriever = TimeRangeRetriever(
            base_retriever=base_retriever,
            index=index,
            similarity_top_k=20
        )
        
        # 测试时间范围查询
        test_queries = [
            "十二里河渡槽进口节制闸在2020-01-01至2020-01-02的调度运行情况",
            "十二里河渡槽2020年1月1日至2日期间运行统计摘要",
            "2020-01-01 12:00:00至2020-01-02 12:00:00十二里河数据",
        ]
        
        for query in test_queries:
            print(f"\n🎯 测试查询: {query}")
            
            try:
                query_bundle = QueryBundle(query_str=query)
                results = time_retriever._retrieve(query_bundle)
                
                print(f"   检索结果: {len(results)} 个")
                
                # 分析结果类型
                summary_count = 0
                excel_count = 0
                
                for result in results[:5]:  # 显示前5个结果
                    content_type = result.node.metadata.get('content_type', 'unknown')
                    score = getattr(result, 'score', 0)
                    
                    if content_type == 'time_series_summary':
                        summary_count += 1
                        print(f"   📊 时序摘要 (分数: {score:.3f}): {result.node.text[:100]}...")
                    elif content_type == 'excel_row':
                        excel_count += 1
                        print(f"   📋 Excel数据 (分数: {score:.3f}): {result.node.text[:100]}...")
                    else:
                        print(f"   📄 其他类型 (分数: {score:.3f}): {result.node.text[:100]}...")
                
                print(f"   结果分布: 时序摘要 {summary_count} 个, Excel数据 {excel_count} 个")
                
                if summary_count > 0:
                    print("   ✅ 成功找到时序摘要，应该能提供完整的时间范围数据")
                elif excel_count > 0:
                    print("   ⚠️ 只找到Excel数据，可能只能提供部分时间点数据")
                else:
                    print("   ❌ 没有找到相关的时间数据")
                    
            except Exception as e:
                print(f"   ❌ 查询失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 测试智能时间范围检索器")
    print("=" * 60)
    
    # 测试时间范围提取
    test_time_range_extraction()
    
    # 测试完整检索
    retrieval_ok = test_full_retrieval()
    
    print(f"\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    
    if retrieval_ok:
        print("✅ 智能时间范围检索器测试通过")
        print("💡 现在系统应该能够:")
        print("   1. 自动识别时间范围查询")
        print("   2. 优先检索时序摘要数据")
        print("   3. 在没有摘要时聚合相关时间点数据")
        print("   4. 提供完整的时间范围分析")
        print("\n🚀 建议重新启动应用测试完整功能!")
    else:
        print("❌ 测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
