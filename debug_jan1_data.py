#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度调试1月1日数据问题
"""
import pandas as pd
from pathlib import Path
import sys
sys.path.append('.')

def check_raw_excel_data():
    """检查原始Excel数据"""
    print("🔍 检查原始Excel数据")
    print("=" * 50)
    
    data_dir = Path("data")
    excel_files = list(data_dir.glob("*.xlsx"))
    
    for excel_file in excel_files:
        if "十二里河" in excel_file.name:
            print(f"📁 文件: {excel_file.name}")
            
            try:
                # 读取原始数据
                df = pd.read_excel(excel_file, dtype=str)
                print(f"   总行数: {len(df)}")
                print(f"   列名: {list(df.columns)}")
                
                # 查找时间列
                time_cols = [col for col in df.columns if '时间' in str(col)]
                if time_cols:
                    time_col = time_cols[0]
                    print(f"   时间列: {time_col}")
                    
                    # 显示所有时间数据
                    print(f"\n   所有时间数据:")
                    for i, time_val in enumerate(df[time_col]):
                        if pd.notna(time_val):
                            print(f"     第{i+1}行: '{time_val}' (类型: {type(time_val)})")
                    
                    # 专门查找1月1日的数据
                    jan1_data = df[df[time_col].astype(str).str.contains('2020-01-01', na=False)]
                    print(f"\n   🎯 2020-01-01相关数据: {len(jan1_data)} 行")
                    
                    if len(jan1_data) > 0:
                        print("   详细数据:")
                        for i, (idx, row) in enumerate(jan1_data.iterrows()):
                            time_val = row[time_col]
                            print(f"     行{idx+1}: 时间='{time_val}'")
                            
                            # 显示其他关键列
                            for col in df.columns:
                                if col != time_col and '流量' in str(col):
                                    print(f"              {col}='{row[col]}'")
                                    break
                    
                    # 查找12:00的数据
                    twelve_data = df[df[time_col].astype(str).str.contains('12', na=False)]
                    print(f"\n   🕐 包含'12'的数据: {len(twelve_data)} 行")
                    
                    if len(twelve_data) > 0:
                        print("   详细数据:")
                        for i, (idx, row) in enumerate(twelve_data.head(5)):
                            time_val = row[time_col]
                            print(f"     行{idx+1}: 时间='{time_val}'")
                
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")

def check_processed_data():
    """检查处理后的数据"""
    print("\n🔍 检查处理后的数据")
    print("=" * 50)
    
    try:
        from app import load_index
        
        # 加载知识库
        index = load_index()
        if index is None:
            print("❌ 知识库加载失败")
            return
        
        # 获取所有节点
        all_nodes = list(index.docstore.docs.values())
        print(f"📊 总节点数: {len(all_nodes)}")
        
        # 查找包含2020-01-01 12:00:00的节点
        jan1_12_nodes = []
        jan1_nodes = []
        
        for node in all_nodes:
            if hasattr(node, 'text'):
                text = node.text
                if "2020-01-01 12:00:00" in text:
                    jan1_12_nodes.append(node)
                elif "2020-01-01" in text and "12" in text:
                    jan1_nodes.append(node)
        
        print(f"🎯 包含'2020-01-01 12:00:00'的节点: {len(jan1_12_nodes)} 个")
        print(f"🎯 包含'2020-01-01'和'12'的节点: {len(jan1_nodes)} 个")
        
        if jan1_12_nodes:
            print("\n✅ 找到精确匹配的节点:")
            for i, node in enumerate(jan1_12_nodes[:3]):
                print(f"   节点{i+1}: {node.text[:150]}...")
                print(f"   元数据: {node.metadata}")
        
        elif jan1_nodes:
            print("\n⚠️ 找到部分匹配的节点:")
            for i, node in enumerate(jan1_nodes[:3]):
                print(f"   节点{i+1}: {node.text[:150]}...")
                print(f"   元数据: {node.metadata}")
        
        else:
            print("\n❌ 没有找到任何相关节点")
            
            # 显示一些样本节点
            print("\n📋 样本节点 (前5个):")
            for i, node in enumerate(all_nodes[:5]):
                if hasattr(node, 'text'):
                    print(f"   节点{i+1}: {node.text[:100]}...")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

def test_time_format_function():
    """测试时间格式化函数"""
    print("\n🔧 测试时间格式化函数")
    print("=" * 50)
    
    try:
        from app import format_datetime
        
        test_cases = [
            "2020-01-01 12",
            "2020-01-01 00", 
            "2020-01-01 08",
            "2020-01-02 12",
            "2020-01-01 12:00:00",
            "invalid"
        ]
        
        print("格式化测试:")
        for test_case in test_cases:
            result = format_datetime(test_case)
            status = "✅" if result.endswith(":00:00") else ("⚪" if result == test_case else "❌")
            print(f"   '{test_case}' → '{result}' {status}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🎯 深度调试1月1日数据问题")
    print("=" * 60)
    
    # 1. 检查原始Excel数据
    check_raw_excel_data()
    
    # 2. 测试时间格式化函数
    test_time_format_function()
    
    # 3. 检查处理后的数据
    check_processed_data()
    
    print(f"\n" + "=" * 60)
    print("📋 调试总结")
    print("=" * 60)
    print("请查看上述输出，确定问题所在:")
    print("1. 原始Excel中是否真的有2020-01-01 12的数据?")
    print("2. 时间格式化函数是否正常工作?")
    print("3. 知识库中是否存储了正确格式的数据?")

if __name__ == "__main__":
    main()
