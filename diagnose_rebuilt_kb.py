#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断重建后的知识库
"""
import sys
sys.path.append('.')

def check_rebuilt_kb():
    """检查重建后的知识库"""
    print("🔍 检查重建后的知识库")
    print("=" * 50)
    
    try:
        from app import load_index
        
        # 加载知识库
        print("📚 加载知识库...")
        index = load_index()
        
        if index is None:
            print("❌ 知识库加载失败")
            return False
        
        print("✅ 知识库加载成功")
        
        # 获取所有节点
        all_nodes = list(index.docstore.docs.values())
        print(f"📊 总节点数: {len(all_nodes)}")
        
        # 查找Excel相关节点
        excel_nodes = [node for node in all_nodes if 
                      node.metadata.get('content_type') == 'excel_row']
        print(f"📊 Excel节点数: {len(excel_nodes)}")
        
        # 查找十二里河相关节点
        shierlihe_nodes = []
        jan1_12_nodes = []
        
        for node in excel_nodes:
            if "十二里河" in node.text:
                shierlihe_nodes.append(node)
                
                # 检查是否包含目标时间
                if "2020-01-01" in node.text and "12:00:00" in node.text:
                    jan1_12_nodes.append(node)
        
        print(f"🏗️ 十二里河节点数: {len(shierlihe_nodes)}")
        print(f"🎯 2020-01-01 12:00:00节点数: {len(jan1_12_nodes)}")
        
        if jan1_12_nodes:
            print("\n✅ 找到目标时间节点:")
            for i, node in enumerate(jan1_12_nodes[:3], 1):
                print(f"   节点{i}: {node.text[:100]}...")
        else:
            print("\n❌ 未找到2020-01-01 12:00:00节点")
            
            # 显示十二里河的时间分布
            if shierlihe_nodes:
                print("\n📅 十二里河时间分布分析:")
                time_points = set()
                
                for node in shierlihe_nodes[:10]:  # 检查前10个节点
                    import re
                    # 查找时间模式
                    time_matches = re.findall(r'2020-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', node.text)
                    time_points.update(time_matches)
                
                sorted_times = sorted(time_points)
                print(f"   发现时间点: {len(sorted_times)} 个")
                
                # 显示2020-01-01的时间点
                jan1_times = [t for t in sorted_times if t.startswith("2020-01-01")]
                print(f"   2020-01-01时间点: {len(jan1_times)} 个")
                
                if jan1_times:
                    for time_point in jan1_times:
                        print(f"     {time_point}")
                else:
                    print("     ❌ 没有2020-01-01的时间点")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_retrieval():
    """测试检索功能"""
    print("\n🔍 测试检索功能")
    print("=" * 50)
    
    try:
        from app import load_index
        
        index = load_index()
        if index is None:
            print("❌ 知识库不可用")
            return False
        
        # 创建检索器
        retriever = index.as_retriever(similarity_top_k=20)  # 增加检索数量
        
        # 测试不同的查询
        test_queries = [
            "十二里河渡槽 2020-01-01 12:00:00",
            "十二里河 2020年1月1日12点",
            "2020-01-01 12:00:00 十二里河",
            "十二里河渡槽进口节制闸 1月1日12时",
            "十二里河 12:00:00",
            "2020-01-01 十二里河"
        ]
        
        for query in test_queries:
            print(f"\n🔍 查询: {query}")
            
            try:
                results = retriever.retrieve(query)
                print(f"   检索结果: {len(results)} 个")
                
                # 检查是否有相关结果
                relevant_results = []
                for result in results:
                    if ("十二里河" in result.text and 
                        "2020-01-01" in result.text and 
                        "12:00:00" in result.text):
                        relevant_results.append(result)
                
                if relevant_results:
                    print(f"   ✅ 找到相关结果: {len(relevant_results)} 个")
                    best_result = relevant_results[0]
                    print(f"   最佳匹配: {best_result.text[:150]}...")
                    print(f"   相关度分数: {getattr(best_result, 'score', 'N/A')}")
                    break  # 找到就停止
                else:
                    print(f"   ❌ 无相关结果")
                    
                    # 显示最相关的结果
                    if results:
                        best_result = results[0]
                        print(f"   最相关结果: {best_result.text[:100]}...")
            
            except Exception as e:
                print(f"   ❌ 查询失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检索测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 诊断重建后的知识库")
    print("=" * 60)
    
    # 检查知识库状态
    kb_ok = check_rebuilt_kb()
    
    if kb_ok:
        # 测试检索
        retrieval_ok = test_retrieval()
        
        print(f"\n" + "=" * 60)
        print("📋 诊断结果")
        print("=" * 60)
        
        if retrieval_ok:
            print("✅ 知识库和检索功能正常")
            print("💡 如果仍然查不到数据，可能是:")
            print("   1. 查询方式问题 - 尝试不同的关键词组合")
            print("   2. 相关度阈值过高 - 检查RELEVANCE_THRESHOLD设置")
            print("   3. 时间格式仍有问题 - 需要进一步调试")
        else:
            print("❌ 检索功能有问题")
            print("💡 建议:")
            print("   1. 检查ChromaDB是否正常工作")
            print("   2. 确认向量模型是否正确加载")
            print("   3. 检查网络连接和模型文件")
    else:
        print("\n❌ 知识库有问题，需要重新构建")

if __name__ == "__main__":
    main()
