#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时间格式修复
"""
import pandas as pd
import re
from functools import lru_cache

@lru_cache(maxsize=128)
def format_datetime(value_str):
    try:
        # 先尝试标准解析
        dt = pd.to_datetime(value_str, errors='coerce')
        if pd.notna(dt):
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        
        # 如果标准解析失败，尝试处理特殊格式
        # 处理 "2020-01-01 12" 这种格式（缺少 :00:00）
        if isinstance(value_str, str) and len(value_str.strip()) > 0:
            # 匹配 "YYYY-MM-DD HH" 格式
            pattern = r'^(\d{4}-\d{2}-\d{2})\s+(\d{1,2})$'
            match = re.match(pattern, value_str.strip())
            if match:
                date_part = match.group(1)
                hour_part = match.group(2).zfill(2)  # 补零到2位
                standardized = f"{date_part} {hour_part}:00:00"
                # 再次尝试解析标准化后的时间
                dt = pd.to_datetime(standardized, errors='coerce')
                if pd.notna(dt):
                    return dt.strftime('%Y-%m-%d %H:%M:%S')
    except Exception:
        pass
    return value_str

def test_time_formats():
    """测试各种时间格式"""
    print("🧪 测试时间格式标准化")
    print("=" * 40)
    
    test_cases = [
        "2020-01-01 12",      # 您的Excel格式
        "2020-01-01 00",      # 零点格式
        "2020-01-01 08",      # 8点格式
        "2020-01-01 12:00:00", # 标准格式
        "2020-01-01 12:00",    # 常见格式
        "2020/01/01 12:00",    # 斜杠格式
        "invalid_time",        # 无效格式
        "",                    # 空字符串
    ]
    
    for test_case in test_cases:
        result = format_datetime(test_case)
        print(f"输入: '{test_case}' → 输出: '{result}'")
    
    print(f"\n✅ 关键测试:")
    excel_format = "2020-01-01 12"
    standardized = format_datetime(excel_format)
    print(f"Excel格式: '{excel_format}' → 标准化: '{standardized}'")
    
    # 测试检索匹配
    query_format = "2020-01-01 12:00:00"
    print(f"查询格式: '{query_format}'")
    print(f"匹配结果: {'✅ 匹配' if standardized == query_format else '❌ 不匹配'}")

if __name__ == "__main__":
    test_time_formats()
