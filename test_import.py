#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的导入测试
"""
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

def test_import():
    """测试函数导入"""
    try:
        from app import create_enhanced_excel_description
        print("✅ 函数导入成功")
        
        # 测试函数调用
        import pandas as pd
        
        # 模拟数据
        sample_data = {
            '时间': '2020-01-01 00',
            '闸前水位(m)': '146.73',
            '闸后水位(m)': '146.08', 
            '开度(mm)': '2070/2100',
            '瞬时流量(m³/s)': '176.51',
            '闸前与设计水位差(m)': '0.07'
        }
        
        df = pd.DataFrame([sample_data])
        columns = list(sample_data.keys())
        
        # 调用函数
        result = create_enhanced_excel_description(
            df.iloc[0], columns, "陶岔渠首引水闸时序数据.xlsx", "陶岔渠首引水闸", 2
        )
        
        print("✅ 函数调用成功")
        print(f"结果: {result}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 函数调用失败: {e}")
        return False

if __name__ == "__main__":
    test_import()
