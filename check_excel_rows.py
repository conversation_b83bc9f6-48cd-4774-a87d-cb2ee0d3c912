#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Excel数据行数
"""
import pandas as pd
from pathlib import Path

def check_excel_data():
    """检查Excel数据行数和时序摘要生成条件"""
    print("📊 检查Excel数据行数")
    print("=" * 40)
    
    data_dir = Path("data")
    excel_files = list(data_dir.glob("*.xlsx"))
    
    for excel_file in excel_files:
        if "十二里河" in excel_file.name:
            print(f"📁 文件: {excel_file.name}")
            
            try:
                df = pd.read_excel(excel_file, dtype=str)
                print(f"   总行数: {len(df)}")
                print(f"   列数: {len(df.columns)}")
                
                # 检查时序摘要生成条件
                if len(df) > 5:
                    print("   ✅ 满足时序摘要生成条件 (>5行)")
                else:
                    print("   ❌ 不满足时序摘要生成条件 (需要>5行)")
                    print("   💡 这就是为什么只能看到单条数据的原因！")
                
                # 显示时间范围
                time_cols = [col for col in df.columns if '时间' in str(col)]
                if time_cols:
                    time_col = time_cols[0]
                    print(f"   时间列: {time_col}")
                    
                    # 显示时间范围
                    time_data = df[time_col].dropna()
                    if len(time_data) > 0:
                        print(f"   时间范围:")
                        print(f"     起始: {time_data.iloc[0]}")
                        print(f"     结束: {time_data.iloc[-1]}")
                        print(f"     时间点数: {len(time_data)}")
                        
                        # 显示所有时间点
                        print(f"   所有时间点:")
                        for i, time_val in enumerate(time_data):
                            print(f"     {i+1}. {time_val}")
                
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")

if __name__ == "__main__":
    check_excel_data()
