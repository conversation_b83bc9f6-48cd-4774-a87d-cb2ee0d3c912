#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查当前知识库中的Excel数据格式
"""
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

def check_current_knowledge_base():
    """检查当前知识库中的Excel数据格式"""
    print("🔍 检查当前知识库中的Excel数据格式")
    print("=" * 60)
    
    try:
        from app import load_index
        
        print("1. 加载知识库...")
        index = load_index()
        if index is None:
            print("❌ 知识库加载失败")
            return False
        
        # 获取所有节点
        all_nodes = list(index.docstore.docs.values())
        excel_nodes = [node for node in all_nodes if 
                      node.metadata.get('content_type') == 'excel_row']
        
        print(f"   总节点数: {len(all_nodes)}")
        print(f"   Excel节点数: {len(excel_nodes)}")
        
        if not excel_nodes:
            print("❌ 没有找到Excel节点")
            return False
        
        print("\n2. 分析Excel节点格式...")
        
        # 检查前5个Excel节点的格式
        sample_nodes = excel_nodes[:5]
        
        for i, node in enumerate(sample_nodes, 1):
            print(f"\n   样本{i}:")
            print(f"     文件: {node.metadata.get('source_file', 'unknown')}")
            print(f"     文本: {node.text[:150]}...")
            
            # 检查是否是新格式（包含"运行数据"）
            is_enhanced = "运行数据" in node.text
            format_type = "增强格式" if is_enhanced else "原始格式"
            print(f"     格式: {format_type}")
        
        # 统计格式分布
        enhanced_count = sum(1 for node in excel_nodes[:100] if "运行数据" in node.text)
        original_count = 100 - enhanced_count
        
        print(f"\n3. 格式分布统计 (前100个节点):")
        print(f"   增强格式: {enhanced_count} 个 ({enhanced_count}%)")
        print(f"   原始格式: {original_count} 个 ({original_count}%)")
        
        # 判断是否需要重建
        if enhanced_count == 0:
            print(f"\n❌ 知识库使用的是原始格式，需要重新构建以应用优化")
            return False
        elif enhanced_count < 50:
            print(f"\n⚠️ 知识库部分使用新格式，建议重新构建以保持一致性")
            return False
        else:
            print(f"\n✅ 知识库已使用增强格式，无需重建")
            return True
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_rebuild_options():
    """显示重建选项"""
    print("\n🔧 知识库重建选项")
    print("=" * 60)
    
    print("方式1: 完全重建（推荐）")
    print("  • 删除现有知识库文件")
    print("  • 重新处理所有数据")
    print("  • 应用所有最新优化")
    print("  • 时间: 约10-20分钟")
    
    print("\n方式2: 增量更新")
    print("  • 保留现有数据")
    print("  • 只更新Excel部分")
    print("  • 可能存在格式不一致")
    print("  • 时间: 约5-10分钟")
    
    print("\n方式3: 测试验证")
    print("  • 创建小规模测试知识库")
    print("  • 验证优化效果")
    print("  • 确认后再完全重建")
    print("  • 时间: 约2-5分钟")

def provide_rebuild_commands():
    """提供重建命令"""
    print("\n💻 重建知识库命令")
    print("=" * 60)
    
    print("1. 完全重建知识库:")
    print("   ```bash")
    print("   # 删除现有知识库")
    print("   rm -rf storage/")
    print("   rm -rf chroma_db/")
    print("   ")
    print("   # 运行构建脚本")
    print("   python build_knowledge_base.py")
    print("   ```")
    
    print("\n2. 或者在Streamlit界面中:")
    print("   • 启动应用: streamlit run app.py")
    print("   • 在侧边栏找到'重建知识库'按钮")
    print("   • 点击重建并等待完成")
    
    print("\n3. 验证重建效果:")
    print("   ```bash")
    print("   python check_current_kb.py")
    print("   ```")

def main():
    """主函数"""
    print("🎯 知识库状态检查与重建指南")
    print("=" * 70)
    
    # 检查当前状态
    needs_rebuild = not check_current_knowledge_base()
    
    if needs_rebuild:
        show_rebuild_options()
        provide_rebuild_commands()
        
        print("\n" + "=" * 70)
        print("📋 重建建议")
        print("=" * 70)
        print("✅ 推荐使用'完全重建'方式")
        print("✅ 重建后Excel检索效果将显著提升")
        print("✅ 所有优化（语义化描述、去重等）将生效")
        print("⚠️ 重建过程中请勿关闭程序")
        print("⚠️ 建议在非高峰时间进行重建")
        
    else:
        print("\n" + "=" * 70)
        print("🎉 知识库状态良好")
        print("=" * 70)
        print("✅ 当前知识库已使用增强格式")
        print("✅ Excel优化已生效")
        print("✅ 无需重新构建")
    
    print(f"\n💡 提示: 如果您刚刚修改了代码，建议重建知识库以确保所有优化生效")

if __name__ == "__main__":
    main()
