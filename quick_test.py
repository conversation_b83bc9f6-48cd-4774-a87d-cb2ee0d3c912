#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试智能检索器
"""
import sys
sys.path.append('.')

def test_time_range_detection():
    """测试时间范围检测"""
    print("🧪 测试时间范围检测")
    print("=" * 40)
    
    from app import TimeRangeRetriever
    
    # 创建临时实例
    class MockRetriever:
        def retrieve(self, query_bundle):
            return []
    
    class MockIndex:
        pass
    
    retriever = TimeRangeRetriever(MockRetriever(), MockIndex())
    
    # 测试您的具体查询
    test_query = "十二里河渡槽进口节制闸在2020-01-01至2020-01-02的调度运行情况"
    print(f"🔍 测试查询: {test_query}")
    
    time_info = retriever._extract_time_range(test_query)
    print(f"🎯 检测结果: {time_info}")
    
    if time_info and time_info.get('has_time_range'):
        print("✅ 成功识别为时间范围查询")
        print(f"   起始时间: {time_info.get('start_time')}")
        print(f"   结束时间: {time_info.get('end_time')}")
    else:
        print("❌ 未能识别为时间范围查询")

def check_data_existence():
    """检查数据是否存在"""
    print("\n🔍 检查数据存在性")
    print("=" * 40)
    
    try:
        from app import load_index
        
        index = load_index()
        if index is None:
            print("❌ 知识库加载失败")
            return
        
        # 获取所有节点
        all_nodes = list(index.docstore.docs.values())
        print(f"📊 总节点数: {len(all_nodes)}")
        
        # 查找包含2020-01-01的节点
        jan1_nodes = []
        jan1_12_nodes = []
        
        for node in all_nodes:
            if hasattr(node, 'text') and node.text:
                text = node.text
                if "2020-01-01" in text:
                    jan1_nodes.append(node)
                    if ("12:00:00" in text or " 12 " in text or 
                        "12点" in text or "12时" in text):
                        jan1_12_nodes.append(node)
        
        print(f"🎯 包含'2020-01-01'的节点: {len(jan1_nodes)} 个")
        print(f"🎯 包含'2020-01-01'和'12'的节点: {len(jan1_12_nodes)} 个")
        
        if jan1_12_nodes:
            print("\n✅ 找到1月1日12点数据:")
            for i, node in enumerate(jan1_12_nodes[:2]):
                print(f"   节点{i+1}: {node.text[:150]}...")
                print(f"   类型: {node.metadata.get('content_type', 'unknown')}")
        else:
            print("\n❌ 没有找到1月1日12点数据")
            
            if jan1_nodes:
                print("\n但找到了其他1月1日数据:")
                for i, node in enumerate(jan1_nodes[:2]):
                    print(f"   节点{i+1}: {node.text[:150]}...")
        
        return len(jan1_12_nodes) > 0
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 快速测试智能检索器")
    print("=" * 50)
    
    # 1. 测试时间范围检测
    test_time_range_detection()
    
    # 2. 检查数据存在性
    has_data = check_data_existence()
    
    print(f"\n" + "=" * 50)
    print("📋 测试结果")
    print("=" * 50)
    
    if has_data:
        print("✅ 数据存在，问题可能在检索或提示词")
        print("💡 建议:")
        print("   1. 重新启动应用: streamlit run app.py")
        print("   2. 查看控制台日志，确认TimeRangeRetriever是否被调用")
        print("   3. 使用查询: '十二里河渡槽进口节制闸在2020-01-01至2020-01-02的调度运行情况'")
    else:
        print("❌ 数据不存在，需要重新构建知识库")
        print("💡 建议:")
        print("   1. 运行: python rebuild_kb.py")
        print("   2. 重新启动应用: streamlit run app.py")

if __name__ == "__main__":
    main()
