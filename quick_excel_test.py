#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速Excel检索效果测试
"""
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

def test_excel_queries():
    """测试Excel相关查询"""
    print("🔍 Excel数据检索快速测试")
    print("=" * 50)
    
    try:
        from app import load_index, OptimizedModelManager
        
        # 加载知识库和模型
        print("1. 加载系统组件...")
        index = load_index()
        if index is None:
            print("❌ 知识库加载失败")
            return
        
        model_manager = OptimizedModelManager()
        retriever = index.as_retriever(similarity_top_k=5)
        
        # 统计Excel节点
        all_nodes = list(index.docstore.docs.values())
        excel_nodes = [node for node in all_nodes if 
                      node.metadata.get('content_type') == 'excel_row']
        
        print(f"   知识库总节点: {len(all_nodes)}")
        print(f"   Excel节点: {len(excel_nodes)}")
        
        # 测试查询
        test_queries = [
            "陶岔渠首引水闸的流量数据",
            "2020年1月的水位情况", 
            "闸门开度信息",
            "瞬时流量超过180的数据",
            "水位差异数据"
        ]
        
        print("\n2. 执行检索测试...")
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n   查询{i}: {query}")
            
            try:
                results = retriever.retrieve(query)
                excel_results = [r for r in results if 
                               r.metadata.get('content_type') == 'excel_row']
                
                print(f"     总结果: {len(results)}, Excel结果: {len(excel_results)}")
                
                if excel_results:
                    # 显示最相关的Excel结果
                    top_result = excel_results[0]
                    score = getattr(top_result, 'score', 'N/A')
                    source = top_result.metadata.get('source_file', 'unknown')
                    
                    print(f"     最佳匹配 (相关度: {score}):")
                    print(f"       文件: {source}")
                    print(f"       内容: {top_result.text[:100]}...")
                    
                    # 分析内容
                    if "瞬时流量" in top_result.text:
                        import re
                        flow_match = re.search(r"瞬时流量\(m³/s\)': '([\d.]+)'", top_result.text)
                        if flow_match:
                            flow_value = flow_match.group(1)
                            print(f"       提取流量: {flow_value} m³/s")
                else:
                    print(f"     ⚠️ 未找到Excel相关结果")
                    # 显示其他类型的结果
                    if results:
                        other_result = results[0]
                        content_type = other_result.metadata.get('content_type', 'unknown')
                        print(f"     找到其他类型结果: {content_type}")
                        
            except Exception as e:
                print(f"     ❌ 查询失败: {str(e)}")
        
        # 分析Excel数据的检索特点
        print("\n3. Excel数据特点分析...")
        if excel_nodes:
            sample_node = excel_nodes[0]
            print(f"   Excel节点文本长度: {len(sample_node.text)} 字符")
            print(f"   文本格式示例: {sample_node.text[:150]}...")
            
            # 统计不同文件的Excel数据
            file_stats = {}
            for node in excel_nodes[:100]:  # 只分析前100个
                source = node.metadata.get('source_file', 'unknown')
                file_stats[source] = file_stats.get(source, 0) + 1
            
            print(f"   Excel文件分布:")
            for file, count in file_stats.items():
                print(f"     {file}: {count} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_excel_text_format():
    """分析Excel数据的文本格式"""
    print("\n📝 Excel文本格式分析")
    print("=" * 50)
    
    try:
        from app import load_index
        
        index = load_index()
        if index is None:
            return
        
        # 获取Excel节点样本
        all_nodes = list(index.docstore.docs.values())
        excel_nodes = [node for node in all_nodes if 
                      node.metadata.get('content_type') == 'excel_row']
        
        if not excel_nodes:
            print("没有找到Excel节点")
            return
        
        print(f"分析 {len(excel_nodes)} 个Excel节点...")
        
        # 分析文本长度分布
        lengths = [len(node.text) for node in excel_nodes[:100]]
        avg_length = sum(lengths) / len(lengths)
        min_length = min(lengths)
        max_length = max(lengths)
        
        print(f"文本长度统计:")
        print(f"  平均长度: {avg_length:.1f} 字符")
        print(f"  最短: {min_length} 字符")
        print(f"  最长: {max_length} 字符")
        
        # 分析关键词出现频率
        keywords = ['时间', '水位', '流量', '开度', '陶岔', '渡槽', '闸']
        keyword_stats = {kw: 0 for kw in keywords}
        
        for node in excel_nodes[:100]:
            for keyword in keywords:
                if keyword in node.text:
                    keyword_stats[keyword] += 1
        
        print(f"\n关键词出现频率 (前100个节点):")
        for keyword, count in keyword_stats.items():
            percentage = (count / 100) * 100
            print(f"  {keyword}: {count} 次 ({percentage:.1f}%)")
        
        # 显示不同类型的Excel数据样本
        print(f"\nExcel数据样本:")
        file_samples = {}
        for node in excel_nodes[:20]:
            source = node.metadata.get('source_file', 'unknown')
            if source not in file_samples:
                file_samples[source] = node.text
        
        for i, (file, text) in enumerate(file_samples.items(), 1):
            print(f"  样本{i} ({file}):")
            print(f"    {text[:120]}...")
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")

def main():
    """主函数"""
    print("🎯 Excel检索效果快速评估")
    print("=" * 60)
    
    # 执行测试
    success = test_excel_queries()
    
    # 分析文本格式
    analyze_excel_text_format()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 Excel检索效果评估总结")
    print("=" * 60)
    
    if success:
        print("✅ 基础检索功能正常")
        print("\n💡 Excel数据检索特点:")
        print("1. ✅ 数据完整性好 - 所有字段都被保留")
        print("2. ✅ 结构化信息清晰 - 便于精确查找")
        print("3. ⚠️ 自然语言理解有限 - 纯数值数据语义化不足")
        print("4. ⚠️ 时间范围查询困难 - 需要优化时序数据处理")
        
        print("\n🔧 优化建议:")
        print("1. 为数值数据添加语义描述")
        print("2. 创建时间段聚合摘要")
        print("3. 添加数值范围和趋势分析")
        print("4. 优化Excel数据的文本表示格式")
    else:
        print("❌ Excel检索存在问题，需要进一步调试")
    
    print(f"\n🎉 您的Excel数据包含丰富的时序信息，")
    print(f"   通过优化文本表示可以显著提升检索效果！")

if __name__ == "__main__":
    main()
