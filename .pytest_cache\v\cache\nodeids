["diagnose_rebuilt_kb.py::test_retrieval", "final_code_check.py::test_imports", "quick_excel_test.py::test_excel_queries", "test_cache_effectiveness.py::test_embedding_cache", "test_cache_effectiveness.py::test_response_cache", "test_enhanced_excel.py::test_enhanced_description", "test_enhanced_excel.py::test_excel_processing_with_enhancement", "test_fixed_excel.py::test_fixed_description", "test_optimization.py::test_model_loading", "test_optimization.py::test_query_cache", "test_smart_excel.py::test_smart_facility_extraction", "test_time_fix.py::test_time_formats", "test_time_series.py::test_time_series_summary"]