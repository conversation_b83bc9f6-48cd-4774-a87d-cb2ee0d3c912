#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终代码检查 - 验证所有功能是否正常
"""
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

def test_imports():
    """测试关键导入是否正常"""
    print("🔍 测试关键导入")
    print("=" * 40)
    
    try:
        # 测试智能Excel函数导入
        from app import (
            extract_facility_name_smart,
            extract_metrics_smart, 
            extract_time_info_smart,
            create_enhanced_excel_description
        )
        print("✅ 智能Excel函数导入成功")
        
        # 测试其他关键函数
        from app import load_excels, load_index, OptimizedModelManager
        print("✅ 核心功能函数导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_smart_functions():
    """测试智能函数功能"""
    print("\n🧠 测试智能函数功能")
    print("=" * 40)
    
    try:
        from app import (
            extract_facility_name_smart,
            extract_metrics_smart,
            create_enhanced_excel_description
        )
        import pandas as pd
        
        # 测试设施名称提取
        facility_name = extract_facility_name_smart(
            "新建水利工程监测数据.xlsx", 
            "监测数据", 
            ["时间", "流量", "水位"]
        )
        print(f"✅ 设施名称提取: {facility_name}")
        
        # 测试指标提取
        test_data = {
            '时间': '2024-07-27 15:30',
            '瞬时流量(m³/s)': '125.6',
            '水位(m)': '12.45',
            '开度(mm)': '850'
        }
        
        df = pd.DataFrame([test_data])
        metrics = extract_metrics_smart(df.iloc[0], list(test_data.keys()))
        print(f"✅ 指标提取: {metrics}")
        
        # 测试完整描述生成
        description = create_enhanced_excel_description(
            df.iloc[0], 
            list(test_data.keys()),
            "测试水利设施数据.xlsx",
            "测试数据",
            1
        )
        print(f"✅ 完整描述: {description}")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_excel_processing():
    """测试Excel处理流程"""
    print("\n📊 测试Excel处理流程")
    print("=" * 40)
    
    try:
        # 检查data目录
        data_dir = Path("data")
        if not data_dir.exists():
            print("⚠️ data目录不存在，跳过Excel处理测试")
            return True
        
        excel_files = list(data_dir.glob("*.xlsx"))
        if not excel_files:
            print("⚠️ 没有Excel文件，跳过Excel处理测试")
            return True
        
        print(f"📁 找到 {len(excel_files)} 个Excel文件")
        
        # 测试加载函数
        from app import load_excels
        
        # 只测试第一个文件
        test_file = excel_files[0]
        print(f"🧪 测试文件: {test_file.name}")
        
        # 创建临时目录只包含一个文件进行测试
        import tempfile
        import shutil
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_file = Path(temp_dir) / test_file.name
            shutil.copy2(test_file, temp_file)
            
            # 测试加载
            data = load_excels(temp_dir)
            
            if data:
                print(f"✅ Excel加载成功: {len(data)} 条记录")
                
                # 检查第一条记录的格式
                if data:
                    sample = data[0]
                    content = sample.get('content', '')
                    
                    # 检查是否使用了增强格式
                    is_enhanced = '运行数据' in content
                    format_type = "增强格式" if is_enhanced else "原始格式"
                    
                    print(f"✅ 数据格式: {format_type}")
                    print(f"✅ 样本内容: {content[:100]}...")
                    
                return True
            else:
                print("❌ Excel加载返回空数据")
                return False
        
    except Exception as e:
        print(f"❌ Excel处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_syntax_errors():
    """检查语法错误"""
    print("\n🔧 检查语法错误")
    print("=" * 40)
    
    try:
        import ast
        
        with open('app.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 尝试解析AST
        ast.parse(code)
        print("✅ 语法检查通过")
        
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"   行号: {e.lineno}")
        print(f"   位置: {e.offset}")
        return False
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 最终代码检查")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("语法检查", check_syntax_errors),
        ("导入测试", test_imports),
        ("智能函数测试", test_smart_functions),
        ("Excel处理测试", test_excel_processing)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 执行 {test_name}...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 执行失败: {e}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 最终检查结果")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有检查通过！代码状态良好")
        print("\n📝 主要功能:")
        print("  ✅ 智能设施名称识别")
        print("  ✅ 通用指标提取")
        print("  ✅ 增强Excel描述生成")
        print("  ✅ 语法正确无错误")
        
        print("\n🚀 下一步:")
        print("  1. 重新构建知识库以应用优化")
        print("  2. 测试实际检索效果")
        print("  3. 添加新Excel文件验证通用性")
        
    else:
        print(f"\n⚠️ {total - passed} 个测试失败，需要修复")
        
        failed_tests = [name for name, result in results.items() if not result]
        print(f"失败的测试: {', '.join(failed_tests)}")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
